#!/usr/bin/env python3
"""
إصلاح مصادقة Blogger
يساعد في إعادة المصادقة مع المستخدم الصحيح الذي يملك صلاحيات الكتابة
"""

import json
import os
from blogger_auth_manager import BloggerAuthManager
from utils.logger import logger
import config

def fix_blogger_authentication():
    """إصلاح مصادقة Blogger"""
    
    print("🔧 إصلاح مصادقة Blogger")
    print("=" * 50)
    
    print("📋 المشكلة الحالية:")
    print("   - يمكن قراءة المدونة لكن لا يمكن الكتابة فيها")
    print("   - المستخدم المصادق عليه ليس مالك المدونة")
    print("   - نحتاج لإعادة المصادقة مع المستخدم الصحيح")
    
    print(f"\n📝 معلومات المدونة الحالية:")
    print(f"   - Blog ID: {config.BLOG_ID}")
    print(f"   - رابط المدونة: http://football774.blogspot.com/")
    
    print("\n💡 الحلول المتاحة:")
    print("1. إعادة المصادقة مع حساب مالك المدونة")
    print("2. إضافة المستخدم الحالي كمؤلف في المدونة")
    print("3. إنشاء مدونة جديدة")
    
    print("\n🔄 سنبدأ بإعادة المصادقة...")
    
    # إنشاء مدير المصادقة
    auth_manager = BloggerAuthManager()
    
    # حذف Token الحالي
    print("\n1️⃣ حذف Token الحالي...")
    try:
        # حذف من متغير البيئة
        if os.path.exists('.env'):
            with open('.env', 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            with open('.env', 'w', encoding='utf-8') as f:
                for line in lines:
                    if not line.startswith('GOOGLE_OAUTH_TOKEN='):
                        f.write(line)
            
            print("✅ تم حذف Token من ملف .env")
        
    except Exception as e:
        print(f"⚠️ خطأ في حذف Token: {e}")
    
    # بدء عملية مصادقة جديدة
    print("\n2️⃣ بدء عملية مصادقة جديدة...")
    print("📱 سيتم فتح متصفح الويب")
    print("🔑 يرجى تسجيل الدخول بحساب مالك المدونة")
    print("⚠️ تأكد من استخدام الحساب الذي أنشأ المدونة")
    
    input("\n⏸️ اضغط Enter للمتابعة...")
    
    # إعادة المصادقة
    creds = auth_manager.authenticate_new_user()
    
    if creds:
        print("\n✅ تمت المصادقة بنجاح!")
        
        # اختبار الصلاحيات الجديدة
        print("\n3️⃣ اختبار الصلاحيات الجديدة...")
        
        success, message = auth_manager.test_blogger_connection()
        
        if success:
            print(f"✅ {message}")
            
            # اختبار إنشاء منشور
            print("\n4️⃣ اختبار إنشاء منشور...")
            
            try:
                service = auth_manager.get_blogger_service()
                
                test_post = {
                    "kind": "blogger#post",
                    "blog": {"id": config.BLOG_ID},
                    "title": "اختبار صلاحيات جديد - سيتم حذفه",
                    "content": "<p>اختبار صلاحيات الكتابة بعد إعادة المصادقة</p>",
                    "labels": ["اختبار"]
                }
                
                # إنشاء كـ draft
                draft_post = service.posts().insert(
                    blogId=config.BLOG_ID, 
                    body=test_post, 
                    isDraft=True
                ).execute()
                
                post_id = draft_post['id']
                print(f"✅ تم إنشاء منشور تجريبي: {post_id}")
                
                # حذف المنشور
                service.posts().delete(blogId=config.BLOG_ID, postId=post_id).execute()
                print("✅ تم حذف المنشور التجريبي")
                
                print("\n🎉 تم إصلاح المصادقة بنجاح!")
                print("✅ النظام جاهز الآن للنشر على Blogger")
                
                return True
                
            except Exception as e:
                print(f"❌ ما زالت هناك مشكلة في الصلاحيات: {e}")
                print("\n💡 حلول إضافية:")
                print("1. تأكد من أنك سجلت الدخول بحساب مالك المدونة")
                print("2. تحقق من إعدادات المدونة في Blogger")
                print("3. أضف المستخدم كمؤلف في إعدادات المدونة")
                return False
        else:
            print(f"❌ فشل في اختبار الاتصال: {message}")
            return False
    else:
        print("❌ فشلت عملية المصادقة")
        return False

def show_manual_steps():
    """عرض خطوات يدوية لحل المشكلة"""
    
    print("\n📋 خطوات يدوية لحل مشكلة الصلاحيات:")
    print("=" * 50)
    
    print("1️⃣ تسجيل الدخول إلى Blogger:")
    print("   - اذهب إلى: https://www.blogger.com")
    print("   - سجل الدخول بحساب مالك المدونة")
    
    print("\n2️⃣ التحقق من ملكية المدونة:")
    print("   - ابحث عن مدونة 'Football news'")
    print(f"   - تأكد من أن رابطها: http://football774.blogspot.com/")
    
    print("\n3️⃣ إضافة مؤلفين (إذا لزم الأمر):")
    print("   - اذهب إلى إعدادات المدونة")
    print("   - اختر 'المؤلفون'")
    print("   - أضف الحساب المطلوب كمؤلف")
    
    print("\n4️⃣ التحقق من إعدادات الخصوصية:")
    print("   - تأكد من أن المدونة ليست خاصة")
    print("   - تأكد من السماح بالنشر عبر API")
    
    print("\n5️⃣ إعادة تشغيل الأداة:")
    print("   - شغل: python fix_blogger_auth.py")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 أداة إصلاح مصادقة Blogger")
    
    print("\nاختر العملية المطلوبة:")
    print("1. إعادة المصادقة التلقائية")
    print("2. عرض الخطوات اليدوية")
    print("3. الخروج")
    
    choice = input("\nاختيارك (1-3): ").strip()
    
    if choice == "1":
        success = fix_blogger_authentication()
        if not success:
            print("\n💡 جرب الخطوات اليدوية إذا استمرت المشكلة")
            show_manual_steps()
    elif choice == "2":
        show_manual_steps()
    elif choice == "3":
        print("👋 وداعاً!")
    else:
        print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    main()
