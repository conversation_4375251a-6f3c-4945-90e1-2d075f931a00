#!/usr/bin/env python3
"""
اختبار صلاحيات Blogger API
يتحقق من قدرة المستخدم على الوصول للمدونة والكتابة فيها
"""

import json
import sys
import os
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from utils.logger import logger
import config

def test_blogger_permissions():
    """اختبار صلاحيات Blogger API"""
    
    print("🔍 اختبار صلاحيات Blogger API...")
    print("=" * 50)
    
    # 1. التحقق من وجود المتغيرات المطلوبة
    print("1️⃣ التحقق من المتغيرات البيئية...")
    
    if not config.GOOGLE_OAUTH_TOKEN:
        print("❌ GOOGLE_OAUTH_TOKEN غير موجود")
        return False
    
    if not config.BLOG_ID:
        print("❌ BLOG_ID غير موجود")
        return False
    
    print(f"✅ BLOG_ID: {config.BLOG_ID}")
    print("✅ GOOGLE_OAUTH_TOKEN موجود")
    
    # 2. إنشاء credentials
    print("\n2️⃣ إنشاء credentials...")
    
    try:
        token_data = json.loads(config.GOOGLE_OAUTH_TOKEN)
        creds = Credentials.from_authorized_user_info(
            token_data, 
            ['https://www.googleapis.com/auth/blogger']
        )
        print("✅ تم إنشاء credentials بنجاح")
    except Exception as e:
        print(f"❌ فشل في إنشاء credentials: {e}")
        return False
    
    # 3. التحقق من صحة Token
    print("\n3️⃣ التحقق من صحة Token...")
    
    if not creds.valid:
        if creds.expired and creds.refresh_token:
            print("🔄 Token منتهي الصلاحية، محاولة التجديد...")
            try:
                creds.refresh(Request())
                print("✅ تم تجديد Token بنجاح")
            except Exception as e:
                print(f"❌ فشل في تجديد Token: {e}")
                return False
        else:
            print("❌ Token غير صالح ولا يمكن تجديده")
            return False
    else:
        print("✅ Token صالح")
    
    # 4. إنشاء خدمة Blogger
    print("\n4️⃣ إنشاء خدمة Blogger...")
    
    try:
        service = build('blogger', 'v3', credentials=creds)
        print("✅ تم إنشاء خدمة Blogger بنجاح")
    except Exception as e:
        print(f"❌ فشل في إنشاء خدمة Blogger: {e}")
        return False
    
    # 5. اختبار الوصول للمدونة
    print("\n5️⃣ اختبار الوصول للمدونة...")
    
    try:
        blog = service.blogs().get(blogId=config.BLOG_ID).execute()
        blog_name = blog.get('name', 'غير معروف')
        blog_url = blog.get('url', 'غير معروف')
        
        print(f"✅ تم الوصول للمدونة بنجاح")
        print(f"📝 اسم المدونة: {blog_name}")
        print(f"🔗 رابط المدونة: {blog_url}")
        
    except HttpError as e:
        print(f"❌ فشل في الوصول للمدونة: {e}")
        if e.resp.status == 404:
            print("💡 المدونة غير موجودة أو Blog ID خاطئ")
        elif e.resp.status == 403:
            print("💡 لا توجد صلاحية للوصول للمدونة")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False
    
    # 6. اختبار صلاحيات الكتابة
    print("\n6️⃣ اختبار صلاحيات الكتابة...")
    
    try:
        # محاولة الحصول على قائمة المنشورات (يتطلب صلاحية قراءة)
        posts_result = service.posts().list(blogId=config.BLOG_ID, maxResults=1).execute()
        posts = posts_result.get('items', [])
        
        print(f"✅ تم الحصول على قائمة المنشورات ({len(posts)} منشور)")
        
        # محاولة إنشاء منشور تجريبي (draft)
        test_post = {
            "kind": "blogger#post",
            "blog": {"id": config.BLOG_ID},
            "title": "اختبار صلاحيات - سيتم حذفه",
            "content": "<p>هذا منشور تجريبي لاختبار صلاحيات الكتابة. سيتم حذفه تلقائياً.</p>",
            "labels": ["اختبار"]
        }
        
        # إنشاء كـ draft أولاً
        draft_post = service.posts().insert(
            blogId=config.BLOG_ID, 
            body=test_post, 
            isDraft=True
        ).execute()
        
        post_id = draft_post['id']
        print(f"✅ تم إنشاء منشور تجريبي (draft) بنجاح: {post_id}")
        
        # حذف المنشور التجريبي
        service.posts().delete(blogId=config.BLOG_ID, postId=post_id).execute()
        print("✅ تم حذف المنشور التجريبي بنجاح")
        
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للنشر على Blogger")
        return True
        
    except HttpError as e:
        print(f"❌ فشل في اختبار صلاحيات الكتابة: {e}")
        if e.resp.status == 403:
            print("💡 لا توجد صلاحية للكتابة في المدونة")
            print("💡 تأكد من أن المستخدم المصادق عليه هو مالك المدونة أو له صلاحيات الكتابة")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع في اختبار الكتابة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار صلاحيات Blogger API")
    
    success = test_blogger_permissions()
    
    if success:
        print("\n✅ الاختبار مكتمل بنجاح!")
        sys.exit(0)
    else:
        print("\n❌ فشل الاختبار!")
        print("\n💡 نصائح لحل المشاكل:")
        print("1. تأكد من أن Blog ID صحيح")
        print("2. تأكد من أن المستخدم المصادق عليه هو مالك المدونة")
        print("3. تأكد من تفعيل Blogger API في Google Cloud Console")
        print("4. جرب إعادة المصادقة باستخدام blogger_auth_manager.py")
        sys.exit(1)

if __name__ == "__main__":
    main()
